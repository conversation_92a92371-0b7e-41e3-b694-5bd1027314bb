import BigNumber from "bignumber.js";
import BaseSimulate from "../BaseSimulate";
import { TCoinMetadata } from "@/types";
import { Transaction } from "@mysten/sui/transactions";
import { suiClient } from "@/utils/suiClient";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";

export const STEAMM_PACKAGE =
  "0x4dfaa01e9fcc00ab2cd4f4b4dbb46ec502dfaf882ad1d8aa26d973949df41c7c";
export const STEAMM_LENDING_MARKET_TYPE =
  "0xf95b06141ed4a174f239417323bde3f209b972f5930d8521ea38a52aff3a6ddf::suilend::MAIN_POOL";
export const STEAMM_LENDING_MARKET_ID =
  "0x84030d26d85eaa7035084a057f2f11f701b7e2e4eda87551becbc7c97505ece1";

export default class SteammSimulate extends BaseSimulate {
  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    poolId: string,
    coinQuote: TCoinMetadata,
    coinBase: TCoinMetadata,
    isXQuoteToken: boolean,
    feeTier: any,
    gasBasePrice: bigint
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    let coinA, coinB;
    const poolObj = await suiClient.getObject({
      id: poolId,
      options: { showType: true },
    });
    const poolType = poolObj.data?.type || "";
    const typeMatch = poolType.match(/Pool<([^>]+)>/);
    if (!typeMatch) throw new Error("Invalid pool type");
    const typeArgs = typeMatch[1].split(", ");
    const bcoinAType = typeArgs[0];
    const bcoinBType = typeArgs[1];
    const lpTokenType = typeArgs[3];

    const coinAType = isXQuoteToken ? coinQuote.address : coinBase.address;
    const coinBType = isXQuoteToken ? coinBase.address : coinQuote.address;

    const isSuiQuote = coinQuote.address === SUI_TOKEN_ADDRESS_FULL;

    let quoteCoinId: string | undefined;
    if (!isSuiQuote) {
      const coins = await suiClient.getCoins({
        owner: walletAddress,
        coinType: coinQuote.address,
      });

      if (coins.data.length === 0) {
        throw new Error(`No ${coinQuote.address} coin found for dummy sender`);
      }

      quoteCoinId = coins.data[0].coinObjectId;
    }

    if (coinAType == coinQuote?.address) {
      if (isSuiQuote) {
        [coinA] = tx.splitCoins(tx.gas, [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      } else {
        // Split from provided coin for non-SUI quote token
        if (!quoteCoinId)
          throw new Error("quoteCoinId required for non-SUI quote tokens");
        [coinA] = tx.splitCoins(tx.object(quoteCoinId), [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      }
      // Create empty coin B (output)
      coinB = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinBType],
      });
    } else {
      coinA = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [coinAType],
      });
      if (isSuiQuote) {
        [coinB] = tx.splitCoins(tx.gas, [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      } else {
        if (!quoteCoinId)
          throw new Error("quoteCoinId required for non-SUI quote tokens");
        [coinB] = tx.splitCoins(tx.object(quoteCoinId), [
          tx.pure.u64(exactAmountIn.toString()),
        ]);
      }
    }

    tx.moveCall({
      target: `${STEAMM_PACKAGE}::steamm_cpmm_router::buy_exact_in`,
      typeArguments: [
        STEAMM_LENDING_MARKET_TYPE,
        coinAType,
        coinBType,
        bcoinAType,
        bcoinBType,
        lpTokenType,
      ],
      arguments: [
        tx.object(poolId),
        tx.object(feeTier.bank_a_object_id),
        tx.object(feeTier.bank_b_object_id),
        tx.object(STEAMM_LENDING_MARKET_ID),
        coinA,
        coinB,
        tx.object("0x6"),
        tx.pure.u64("0"),
        tx.pure.bool(isXQuoteToken),
        tx.pure.string("-1"), // order id
      ],
    });

    return tx;
  };
}
